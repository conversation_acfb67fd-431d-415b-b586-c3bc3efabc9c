import asyncio
import logging
from typing import List, Optional, Union

import numpy as np
from openai import AsyncAzureOpenAI, RateLimitError, APIError, APIConnectionError

from acva_ai._params import (
    AZURE_API_VERSION,
    AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_ENDPOINT,
    OPENAI_MODEL_ID,
    OPENAI_PRICING,
)
from acva_ai.llm.llm_providers.llm_helpers import (
    LLMError,
    LLMProviderError,
    LLMRateLimitError,
    extract_task_id_from_prompt,
    generate_cache_filepath,
    save_to_cache,
    load_cached_response,
    standardize_response,
    calculate_usage_cost,
    prepare_messages_from_prompt,
    get_log_prefix,
    estimate_tokens_from_text,
)
from acva_ai.llm.rate_limiter import AzureOpenAIRateLimiter, RateLimitConfig
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)

# Initialize the rate limiter with Azure OpenAI limits
rate_limiter = AzureOpenAIRateLimiter(
    {
        "gpt-4o": RateLimitConfig(
            tokens_per_minute=250000,  # 250k tokens per minute
            requests_per_minute=1500,  # 1500 requests per minute
        ),
    }
)


class AzureProvider:
    """Azure OpenAI LLM provider class."""

    def __init__(self):
        """Initialize the Azure provider."""
        self.provider_name = "azure"
        self.model_id = OPENAI_MODEL_ID

        # Initialize Azure OpenAI client
        self.client = AsyncAzureOpenAI(
            api_key=AZURE_OPENAI_API_KEY,
            api_version=AZURE_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
        )

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return self.provider_name

    def get_model_id(self) -> str:
        """Get the model ID."""
        return self.model_id

    async def call_llm(
        self,
        prompt: str,
        max_tokens: int = 5000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        current_retry: int = 0,
        max_retries: int = 3,
        retry_delay: float = 10,
        temperature: float = 0,
    ) -> str:
        """
        Asynchronous call to Azure OpenAI Chat Completion with caching and rate limit handling.

        Args:
            prompt: The prompt to send to the model
            model_id: The model ID (Azure deployment name)
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            current_retry: Current retry attempt (used internally)
            max_retries: Maximum number of retries for rate limit errors
            retry_delay: Initial delay before retrying (will increase exponentially)
            temperature: Temperature parameter for the model

        Returns:
            The model's response as a string
        """
        task_id = extract_task_id_from_prompt(prompt)
        log_prefix = get_log_prefix(task_id)

        # Check cache first
        cache_filepath = generate_cache_filepath(
            prompt, self.model_id, self.provider_name, max_tokens, temperature
        )
        if use_cache:
            cached_data = load_cached_response(cache_filepath)
            if cached_data:
                logger.info(f"{log_prefix}Loaded Azure response from cache.")
                return cached_data.get("response", "")

        # Prepare the messages based on prompt type
        try:
            messages = prepare_messages_from_prompt(prompt)
        except LLMError as e:
            raise LLMProviderError(f"Azure provider error: {e}")

        try:
            # Use Azure OpenAI client instead of direct URL calls
            response = await self.client.chat.completions.create(
                model=self.model_id,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
            )

            # Extract the response text
            response_msg = response.choices[0].message.content

            # Track usage if provided
            if response_usage is not None and response.usage:
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens

                # Calculate and track usage cost using Azure pricing
                from acva_ai._params import AZURE_PRICING

                calculate_usage_cost(
                    model_id=self.model_id,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    pricing_dict=AZURE_PRICING,
                    response_usage=response_usage,
                )

            # Prepare cache data
            cache_data = {
                "response": response_msg,
                "model_id": self.model_id,
                "provider": self.provider_name,
                "usage": {
                    "prompt_tokens": (
                        response.usage.prompt_tokens if response.usage else 0
                    ),
                    "completion_tokens": (
                        response.usage.completion_tokens if response.usage else 0
                    ),
                    "total_tokens": (
                        response.usage.total_tokens if response.usage else 0
                    ),
                },
            }

            # Cache the response
            save_to_cache(cache_filepath, cache_data)

            return response_msg

        except Exception as e:
            # Handle rate limiting errors
            if isinstance(e, RateLimitError):
                if current_retry < max_retries:
                    wait_time = retry_delay * (2**current_retry)
                    logger.warning(
                        f"{log_prefix}Rate limit reached. Waiting {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await self.call_llm(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )
                else:
                    raise LLMRateLimitError(
                        f"Azure rate limit exceeded after {max_retries} retries: {e}"
                    )

            # Handle API errors (server errors, etc.)
            if isinstance(e, (APIError, APIConnectionError)):
                if current_retry < max_retries:
                    wait_time = retry_delay * (2**current_retry)
                    logger.warning(
                        f"{log_prefix}API error: {str(e)}. Waiting {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await self.call_llm(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )
                else:
                    raise LLMProviderError(
                        f"Azure API error after {max_retries} retries: {e}"
                    )

            # For other errors, retry once if we haven't exhausted retries
            if current_retry < max_retries:
                wait_time = retry_delay
                logger.warning(
                    f"{log_prefix}Unexpected error: {str(e)}. Retrying in {wait_time} seconds..."
                )
                await asyncio.sleep(wait_time)
                return await self.call_llm(
                    prompt=prompt,
                    max_tokens=max_tokens,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    current_retry=current_retry + 1,
                    max_retries=max_retries,
                    retry_delay=retry_delay,
                    temperature=temperature,
                )
            else:
                logger.error(
                    f"{log_prefix}Error maintained after {max_retries} retries: {str(e)}"
                )
                raise LLMProviderError(f"Azure provider error: {str(e)}")


def test():
    # This is how to track costs:
    response_usage = ResponseUsage()
    openai = AzureProvider()

    result = asyncio.run(
        openai.call_llm(
            prompt="What is the capital of France?",
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=False,
        )
    )
    print(result)

    print(response_usage)


async def test_rate_limit(num_parallels: int = 2000, num_sequence: int = 30):
    openai = AzureProvider()
    response_usage = ResponseUsage()
    for i in range(num_sequence):
        print(f"Test {i+1} / {num_sequence}")
        tasks = []
        for _ in range(num_parallels):
            tasks.append(
                openai.call_llm(
                    prompt=f"Write me a report on the history of the roman Empire",
                    max_tokens=5000,
                    response_usage=response_usage,
                    use_cache=False,
                    temperature=0.5,
                )
            )
        await asyncio.gather(*tasks)

    print(response_usage)


if __name__ == "__main__":
    test()
